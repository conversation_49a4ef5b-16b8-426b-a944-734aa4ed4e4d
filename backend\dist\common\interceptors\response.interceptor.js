"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const common_response_dto_1 = require("../dto/common-response.dto");
let ResponseInterceptor = class ResponseInterceptor {
    logger = new common_1.Logger('API');
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const startTime = Date.now();
        const { method, url, body, query, params } = request;
        const userAgent = request.get('User-Agent') || '';
        const ip = request.ip || 'unknown';
        return next.handle().pipe((0, operators_1.map)((data) => {
            const duration = Date.now() - startTime;
            if (data instanceof common_response_dto_1.SuccessResponseDto) {
                return data;
            }
            const isApiDataRoute = url.includes('/tasks') ||
                url.includes('/finances') ||
                url.includes('/ideas') ||
                url.includes('/dashboard') ||
                url.includes('/categories') ||
                url.includes('/profile');
            if (isApiDataRoute && data && typeof data === 'object') {
                return data;
            }
            return new common_response_dto_1.SuccessResponseDto(data, 'Operation completed successfully');
        }), (0, operators_1.catchError)((error) => {
            const duration = Date.now() - startTime;
            this.logger.error({
                message: 'Request failed',
                method,
                url,
                error: error.message,
                stack: error.stack,
                duration: `${duration}ms`,
                timestamp: new Date().toISOString(),
            });
            if (error instanceof common_1.HttpException) {
                const status = error.getStatus();
                const errorResponse = error.getResponse();
                return (0, rxjs_1.throwError)(() => new common_1.HttpException(new common_response_dto_1.ErrorResponseDto(error.message, typeof errorResponse === 'string' ? errorResponse : JSON.stringify(errorResponse), status), status));
            }
            return (0, rxjs_1.throwError)(() => new common_1.HttpException(new common_response_dto_1.ErrorResponseDto('Internal server error', error.message, common_1.HttpStatus.INTERNAL_SERVER_ERROR), common_1.HttpStatus.INTERNAL_SERVER_ERROR));
        }));
    }
    sanitizeBody(body) {
        if (!body)
            return body;
        const sensitiveFields = ['password', 'token', 'api_key', 'secret'];
        const sanitized = { ...body };
        for (const field of sensitiveFields) {
            if (sanitized[field]) {
                sanitized[field] = '***REDACTED***';
            }
        }
        return sanitized;
    }
};
exports.ResponseInterceptor = ResponseInterceptor;
exports.ResponseInterceptor = ResponseInterceptor = __decorate([
    (0, common_1.Injectable)()
], ResponseInterceptor);
//# sourceMappingURL=response.interceptor.js.map