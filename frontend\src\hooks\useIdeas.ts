import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import {
  IdeaListResponseDto,
  IdeaResponseDto,
  CreateIdeaDto,
  UpdateIdeaDto,
  IdeaCategoryResponseDto,
  CreateIdeaCategoryDto,
  UpdateIdeaCategoryDto,
  PaginationParams,
} from '../types/api';

// Hook para listar ideas
export const useIdeas = (params?: PaginationParams) => {
  return useQuery({
    queryKey: ['ideas', params],
    queryFn: async (): Promise<IdeaListResponseDto> => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      
      const response = await authenticatedApi.get(`ideas?${searchParams.toString()}`);
      const responseData = await response.json() as IdeaListResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o IdeaListResponseDto
      return responseData;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false, // Evita re-fetch ao focar na janela
  });
};

// Hook para buscar uma idea específica
export const useIdea = (id: number) => {
  return useQuery({
    queryKey: ['ideas', id],
    queryFn: async (): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.get(`ideas/${id}`);
      const responseData = await response.json() as IdeaResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o IdeaResponseDto
      return responseData;
    },
    enabled: !!id,
  });
};

// Hook para criar idea
export const useCreateIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateIdeaDto): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.post('ideas', { json: data });
      const responseData = await response.json() as IdeaResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o IdeaResponseDto
      return responseData;
    },
    onSuccess: () => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para atualizar idea
export const useUpdateIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateIdeaDto }): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.put(`ideas/${id}`, { json: data });
      const responseData = await response.json() as IdeaResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o IdeaResponseDto
      return responseData;
    },
    onSuccess: (data) => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da idea específica
      queryClient.setQueryData(['ideas', data.id], data);
    },
  });
};

// Hook para deletar idea
export const useDeleteIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`ideas/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para marcar/desmarcar idea como favorita
export const useToggleFavoriteIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.patch(`ideas/${id}/favorite`);
      const responseData = await response.json() as IdeaResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o IdeaResponseDto
      return responseData;
    },
    onSuccess: (data) => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da idea específica
      queryClient.setQueryData(['ideas', data.id], data);
    },
  });
};

// Hook para listar categorias de ideas
export const useIdeaCategories = () => {
  return useQuery({
    queryKey: ['idea-categories'],
    queryFn: async (): Promise<IdeaCategoryResponseDto[]> => {
      const response = await authenticatedApi.get('ideas/categories');
      const responseData = await response.json();
      
      // Com a correção no backend, a resposta agora é diretamente o array de categorias
      // Ensure we always return an array
      return Array.isArray(responseData) ? responseData : [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutos (categorias mudam menos)
  });
};

// Hook para criar categoria de idea
export const useCreateIdeaCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateIdeaCategoryDto): Promise<IdeaCategoryResponseDto> => {
      const response = await authenticatedApi.post('ideas/categories', { json: data });
      const responseData = await response.json() as IdeaCategoryResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o IdeaCategoryResponseDto
      return responseData;
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['idea-categories'] });
    },
  });
};

// Hook para atualizar categoria de idea
export const useUpdateIdeaCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateIdeaCategoryDto }): Promise<IdeaCategoryResponseDto> => {
      const response = await authenticatedApi.put(`ideas/categories/${id}`, { json: data });
      const responseData = await response.json() as IdeaCategoryResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o IdeaCategoryResponseDto
      return responseData;
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['idea-categories'] });
    },
  });
};

// Hook para deletar categoria de idea
export const useDeleteIdeaCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`ideas/categories/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['idea-categories'] });
    },
  });
};
