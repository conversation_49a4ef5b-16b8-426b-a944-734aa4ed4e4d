import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import { authenticatedApi } from '../lib/api';
import {
  FinanceListResponseDto,
  FinanceResponseDto,
  CreateFinanceDto,
  UpdateFinanceDto,
  FinanceSummaryDto,
  FinanceCategoryResponseDto,
  CreateFinanceCategoryDto,
  UpdateFinanceCategoryDto,
  PaginationParams,
  AnnualFinancialSummaryDto,
  CategoryDistributionDto,
  AnnualComparisonDto,
} from '../types/api';

// Hook para listar finances
export const useFinances = (params?: PaginationParams) => {
  return useQuery({
    queryKey: ['finances', params],
    queryFn: async (): Promise<FinanceListResponseDto> => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      
      const response = await authenticatedApi.get(`finances?${searchParams.toString()}`);
      const responseData = await response.json() as FinanceListResponseDto;
      
      // Com a correção no backend, a resposta agora é diretamente o FinanceListResponseDto
      return responseData;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false, // Evita re-fetch ao focar na janela
  });
};

// Hook para buscar uma finance específica
export const useFinance = (id: number) => {
  return useQuery({
    queryKey: ['finances', id],
    queryFn: async (): Promise<FinanceResponseDto> => {
      const response = await authenticatedApi.get(`finances/${id}`);
      return response.json();
    },
    enabled: !!id,
  });
};

// Hook para resumo financeiro
export const useFinancesSummary = (startDate?: string, endDate?: string) => {
  return useQuery({
    queryKey: ['finances-summary', startDate, endDate],
    queryFn: async (): Promise<FinanceSummaryDto> => {
      const searchParams = new URLSearchParams();
      if (startDate) searchParams.append('startDate', startDate);
      if (endDate) searchParams.append('endDate', endDate);
      
      const response = await authenticatedApi.get(`finances/summary?${searchParams.toString()}`);
      const responseData = await response.json() as FinanceSummaryDto;
      
      // Com a correção no backend, a resposta agora é diretamente o FinanceSummaryDto
      return responseData;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false, // Evita re-fetch ao focar na janela
  });
};

// Hook para criar finance
export const useCreateFinance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateFinanceDto): Promise<FinanceResponseDto> => {
      const response = await authenticatedApi.post('finances', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das finances, summary e dashboard
      queryClient.invalidateQueries({ queryKey: ['finances'] });
      queryClient.invalidateQueries({ queryKey: ['finances-summary'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para atualizar finance
export const useUpdateFinance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateFinanceDto }): Promise<FinanceResponseDto> => {
      const response = await authenticatedApi.put(`finances/${id}`, { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidar cache das finances, summary e dashboard
      queryClient.invalidateQueries({ queryKey: ['finances'] });
      queryClient.invalidateQueries({ queryKey: ['finances-summary'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da finance específica
      queryClient.setQueryData(['finances', data.id], data);
    },
  });
};

// Hook para deletar finance
export const useDeleteFinance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`finances/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das finances, summary e dashboard
      queryClient.invalidateQueries({ queryKey: ['finances'] });
      queryClient.invalidateQueries({ queryKey: ['finances-summary'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para listar categorias de finances
export const useFinanceCategories = () => {
  return useQuery({
    queryKey: ['finance-categories'],
    queryFn: async (): Promise<FinanceCategoryResponseDto[]> => {
      try {
        const response = await authenticatedApi.get('finances/categories');

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json() as FinanceCategoryResponseDto[];
        
        // Com a correção no backend, a resposta agora é diretamente o array de categorias
        return responseData;
      } catch (error: any) {

        // Melhorar a mensagem de erro para o usuário
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          throw new Error('Erro de conexão com o servidor. Verifique sua conexão com a internet.');
        }

        if (error.message.includes('401')) {
          throw new Error('Sessão expirada. Faça login novamente.');
        }

        if (error.message.includes('403')) {
          throw new Error('Acesso negado. Você não tem permissão para acessar este recurso.');
        }

        if (error.message.includes('404')) {
          throw new Error('Endpoint não encontrado. Verifique se o servidor está configurado corretamente.');
        }

        if (error.message.includes('500')) {
          throw new Error('Erro interno do servidor. Tente novamente mais tarde.');
        }

        throw new Error(error.message || 'Erro desconhecido ao carregar categorias');
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutos (categorias mudam menos)
    retry: (failureCount, error: any) => {
      // Não tentar novamente para erros de autenticação
      if (error.message.includes('401') || error.message.includes('403')) {
        return false;
      }
      // Tentar até 3 vezes para outros erros
      return failureCount < 3;
    },
  });
};

// Hook para criar categoria de finance
export const useCreateFinanceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateFinanceCategoryDto): Promise<FinanceCategoryResponseDto> => {
      const response = await authenticatedApi.post('finances/categories', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['finance-categories'] });
    },
  });
};

// Hook para atualizar categoria de finance
export const useUpdateFinanceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateFinanceCategoryDto }): Promise<FinanceCategoryResponseDto> => {
      const response = await authenticatedApi.put(`finances/categories/${id}`, { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['finance-categories'] });
    },
  });
};

// Hook para deletar categoria de finance
export const useDeleteFinanceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`finances/categories/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['finance-categories'] });
    },
  });
};

// Hook para buscar gastos por categoria
export const useFinanceExpensesByCategory = (startDate?: string, endDate?: string) => {
  return useQuery({
    queryKey: ['finances-expenses-by-category', startDate, endDate],
    queryFn: async (): Promise<{ category: string; amount: number; count: number }[]> => {
      const searchParams = new URLSearchParams();
      if (startDate) searchParams.append('startDate', startDate);
      if (endDate) searchParams.append('endDate', endDate);

      const response = await authenticatedApi.get(`finances/expenses-by-category?${searchParams.toString()}`);
      const responseData = await response.json();

      // Com a correção no backend, a resposta agora é diretamente o array
      const data = responseData;

      // Ensure we always return an array
      return Array.isArray(data) ? data : [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false, // Evita re-fetch ao focar na janela
  });
};

// Hook para buscar resumo anual
export const useAnnualFinancialSummary = (year?: number) => {
  return useQuery({
    queryKey: ['annual-financial-summary', year],
    queryFn: async (): Promise<AnnualFinancialSummaryDto | null> => {
      try {
        const searchParams = new URLSearchParams();
        if (year) searchParams.append('year', year.toString());

        const url = `finances/annual-summary?${searchParams.toString()}`;
        const response = await authenticatedApi.get(url);

        if (!response.ok) {
          if (response.status === 404) {
            return null;
          }
          throw new Error(`Erro ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json() as AnnualFinancialSummaryDto;
        
        // Com a correção no backend, a resposta agora é diretamente o AnnualFinancialSummaryDto
        const data = responseData;

        // Validar estrutura dos dados
        if (!data || typeof data !== 'object') {
          return null;
        }

        // Validar campos obrigatórios
        const requiredFields = ['year', 'monthlyData', 'totals'];
        const missingFields = requiredFields.filter(field => !data.hasOwnProperty(field));
        
        if (missingFields.length > 0) {
          console.warn('Missing fields in annual summary:', missingFields, 'Data:', data);
          // Ainda retorna os dados mesmo com campos ausentes, para permitir fallback
        }

        return data;
      } catch (error: any) {
        // Para erros de rede ou servidor, permitir que o React Query faça retry
        if (error.message.includes('fetch') || error.message.includes('500')) {
          throw error;
        }

        // Para outros erros, retornar null para permitir fallback
        return null;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    retry: (failureCount, error: any) => {
      // Não tentar novamente para erros de autenticação ou 404
      if (error?.message?.includes('401') || error?.message?.includes('403') || error?.message?.includes('404')) {
        return false;
      }

      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    enabled: !!year, // Só executar se year for fornecido
  });
};

// Hook para comparação anual (ano atual vs anterior)
export const useAnnualComparison = (currentYear: number, previousYear: number) => {
  const currentYearQuery = useAnnualFinancialSummary(currentYear);
  const previousYearQuery = useAnnualFinancialSummary(previousYear);
  
  return useQuery({
    queryKey: ['annual-comparison', currentYear, previousYear],
    queryFn: async () => {
      const currentData = currentYearQuery.data;
      const previousData = previousYearQuery.data;
      
      if (!currentData || !previousData) {
        throw new Error('Dados não disponíveis para comparação');
      }
      
      const calculateChange = (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };
      
      return {
        receitas: {
          current: currentData.totals?.income || 0,
          previous: previousData.totals?.income || 0,
          change: calculateChange(currentData.totals?.income || 0, previousData.totals?.income || 0)
        },
        despesas: {
          current: currentData.totals?.expenses || 0,
          previous: previousData.totals?.expenses || 0,
          change: calculateChange(currentData.totals?.expenses || 0, previousData.totals?.expenses || 0)
        },
        economias: {
          current: currentData.totals?.savings || 0,
          previous: previousData.totals?.savings || 0,
          change: calculateChange(currentData.totals?.savings || 0, previousData.totals?.savings || 0)
        },
        saldoMedio: {
          current: currentData.averages?.monthlySavings || 0,
          previous: previousData.averages?.monthlySavings || 0,
          change: calculateChange(currentData.averages?.monthlySavings || 0, previousData.averages?.monthlySavings || 0)
        }
      };
    },
    enabled: !!currentYearQuery.data && !!previousYearQuery.data,
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para buscar categorias de gastos formatadas para gráfico
export const useAnnualCategoryDistribution = (year?: number) => {
  return useQuery({
    queryKey: ['annual-category-distribution', year],
    queryFn: async () => {
      try {
        const searchParams = new URLSearchParams();
        if (year) searchParams.append('year', year.toString());
        
        const url = `finances/annual-category-distribution?${searchParams.toString()}`;
        
        const response = await authenticatedApi.get(url);
        
        if (!response.ok) {
          if (response.status === 404) {
            return [];
          }
          throw new Error(`Erro ${response.status}: ${response.statusText}`);
        }
        
        const responseData = await response.json();
        
        // Com a correção no backend, a resposta agora é diretamente o array de distribuição
        const data = responseData;
        
        // Ensure we always return an array
        return Array.isArray(data) ? data : [];
      } catch (error: any) {
        
        // Para erros de rede, permitir retry
        if (error.message.includes('fetch') || error.message.includes('500')) {
          throw error;
        }
        
        // Para outros erros, retornar array vazio
        return [];
      }
    },
    enabled: !!year,
    staleTime: 10 * 60 * 1000, // 10 minutos
    retry: (failureCount, error: any) => {
      // Não tentar novamente para erros de autenticação ou 404
      if (error?.message?.includes('401') || error?.message?.includes('403') || error?.message?.includes('404')) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
  });
};

// Hook para exportar relatório anual
export const useExportAnnualReport = () => {
  return useMutation({
    mutationFn: async ({ year, format = 'pdf' }: { year: number; format?: 'pdf' | 'excel' }) => {
      const response = await authenticatedApi.get(`finances/annual-report/${year}?format=${format}`, {
        // Configurar para download de arquivo
        headers: {
          'Accept': format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      });
      
      if (!response.ok) {
        throw new Error('Erro ao exportar relatório');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `relatorio-anual-${year}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      return { success: true };
    }
  });
};

// Hook para gerenciar metas anuais dinamicamente (usando localStorage)
export const useAnnualGoalDynamic = (year: number) => {
  const [customGoal, setCustomGoal] = useState<number | null>(() => {
    const stored = localStorage.getItem(`annual-goal-${year}`);
    return stored ? parseFloat(stored) : null;
  });

  const setGoal = useCallback((amount: number) => {
    setCustomGoal(amount);
    localStorage.setItem(`annual-goal-${year}`, amount.toString());
  }, [year]);

  const clearGoal = useCallback(() => {
    setCustomGoal(null);
    localStorage.removeItem(`annual-goal-${year}`);
  }, [year]);

  return {
    customGoal,
    setGoal,
    clearGoal,
    hasCustomGoal: customGoal !== null
  };
};
